<script setup lang="ts">
import { ref, onMounted } from 'vue'

const isLogined = ref(false)
const hasPrivilege = ref(false)
const isChecking = ref(false)
const uploading = ref(false)

function checkShow() {
  // 这个蜂巢配置的结果中，返回show字段，值为bool
 isChecking.value = window.Extention.Honeycomb.getMostApplicableDataUnderModuleGroup({ projectID: '60', moduleGroupID: '55748' });
  console.log('是否显示:', isChecking.value.result);
  // 字符串解析为json
  isChecking.value = JSON.parse(isChecking.value.result).show;
  console.log('是否显示:', isChecking.value);
}

// 检查登录状态
function checkLoginState() {
  try {
    const response = (window as any).Extention.Account.getUserInfo()
    console.log('getUserInfo结果:', response)

    // 检查返回格式 {code: true, result: {...}}
    if (response && response.code && response.result) {
      const result = response.result
      if (result.userId) {
        isLogined.value = true
      } else {
        isLogined.value = false
      }
    } else {
      isLogined.value = false
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
  }
}

// 登录
function login() {


  try {
    const result = (window as any).Extention.Account.login({
      bizSrc: "weblogin",
      loginSrc: "a",
      qrcode: "a",
      from: "a",
      bShowModal: true,
      bTopWindow: false,
      a: "a"
    })
    console.log('登录结果:', result)

    setTimeout(() => {
      checkLoginState()
    }, 1000)
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 退登
function logout() {

  try {
    const result = (window as any).Extention.Account.logout({
      logoutSrc: "weblogout"
    })
    console.log('退登结果:', result)

    setTimeout(() => {
      checkLoginState()
    }, 1000)
  } catch (error) {
    console.error('退登失败:', error)
  }
}

// 检查权限
function checkPrivilege() {

    const result = (window as any).Extention.Account.Privilege.getPrivilegeInfo({
      privilege_ids: ['resume_package_new']
    })
    console.log('权限检查结果:', result)

    if (result && result.code && result.result) {
      const privileges = result.result
      if (privileges.resume_package_new && privileges.resume_package_new.has_privilege) {
        hasPrivilege.value = true
        alert('权限检查成功：您有此权限！')
      } else {
        hasPrivilege.value = false
        alert('权限检查：您暂无此权限，请支付获取')
      }
    } 
  }

// 调起支付
function openPayment() {
    const result = (window as any).Extention.DocerV2.openDocerUnifyPayDlg({
      url: 'https://vip.wps.cn/vcl_svr/static/docerpay'
    })
    console.log('支付调起结果:', result)
    alert('支付页面已打开')
}

// 上传文件到云文档
async function uploadToCloud() {
  // 如果未登录，先调起登录
  if (!isLogined.value) {
    login()
    return
  }

  uploading.value = true

  try {
    // 1. 选择文件
    const fileResult = (window as any).Extention.Common.File.getOpenFileNames({
      caption: "选择要上传的文件",
      filter: "Microsoft Word文件(*.docx);;PDF文件(*.pdf);;所有文件(*.*)"
    })

    console.log('选择文件结果:', fileResult)

    if (!fileResult || !fileResult.result || fileResult.result.length === 0) {
      alert('未选择文件')
      uploading.value = false
      return
    }

    const filePath = fileResult.result[0]
    console.log('选择的文件路径:', filePath)

    // 2. 读取文件内容
    const fileContent = (window as any).Application.FileSystem.ReadFileAsArrayBuffer(filePath)
    console.log('文件内容:', fileContent)

    // 3. 解析文件名和后缀
    const fileName = filePath.split('\\').pop().split('/').pop()
    const lastDotIndex = fileName.lastIndexOf('.')
    const fileNameWithoutExt = fileName.substring(0, lastDotIndex)
    const fileExt = fileName.substring(lastDotIndex)

    console.log('文件名:', fileNameWithoutExt, '后缀:', fileExt)

    // 4. 上传到云文档
    const uploadResult = await uploadFileToCloud(fileContent, fileNameWithoutExt, fileExt)
    console.log('上传结果:', uploadResult)

    if (uploadResult && uploadResult.code === 0) {
      alert('文件上传成功！')
    } else {
      alert('文件上传失败: ' + (uploadResult?.msg || '未知错误'))
    }

  } catch (error) {
    console.error('上传文件失败:', error)
    alert('上传文件失败: ' + error)
  }

  uploading.value = false
}

// 上传文件接口
async function uploadFileToCloud(content: any, fileName: string, fileLast: string) {
  try {
    const url = 'https://assess.docer.wps.cn/share/v2/upload/0'
    const response = await fetch(`${url}?is_normal=1&file_name=${fileName}&file_last=${fileLast}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      body: content,
      credentials: 'include',
    })
    const result = await response.json()
    return result
  } catch (e) {
    console.error(e)
    throw e
  }
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginState()
  checkShow()
  // 监听登录
  try {
    (window as any).Extention.Account.addLoginListener(function(data: any) {
      console.log('登录事件:', data)
      setTimeout(() => checkLoginState(), 500)
    })

    // 监听退登
    (window as any).Extention.Account.addLogoutListener(function(data: any) {
      console.log('退登事件:', data)
      setTimeout(() => checkLoginState(), 500)
    })

    console.log('事件监听器注册成功')
  } catch (error) {
    console.error('注册事件监听器失败:', error)
  }
})
</script>

<template>
  <div class="about">
    <h1>WPS 2.0 JSAPI 登录</h1>
    <div class="buttons">
      <button @click="login" :disabled="isLogined">
                  登录
      </button>

      <button @click="logout" :disabled="!isLogined">
                  退登
      </button>
    </div>

    <div class="buttons">
      <button v-if="isChecking" @click="checkPrivilege" :disabled="!isLogined">
        检查权限
      </button>

      <button @click="openPayment" :disabled="!isLogined">
        调起支付
      </button>
    </div>
  </div>
</template>

<style>
.about {
  padding: 20px;
  text-align: center;
}

.status {
  margin: 20px 0;
  font-size: 18px;
}

.buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>

