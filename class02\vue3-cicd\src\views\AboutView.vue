<script setup lang="ts">
import { ref, onMounted } from 'vue'

const isLogined = ref(false)
const loginStatus = ref('未登录')
const loading = ref(false)

// 检查登录状态
function checkLoginState() {
  loading.value = true
  try {
    const response = (window as any).Extention.Account.getUserInfo()
    console.log('getUserInfo结果:', response)

    // 检查返回格式 {code: true, result: {...}}
    if (response && response.code && response.result) {
      const result = response.result
      if (result.login === true || result.userId) {
        isLogined.value = true
        loginStatus.value = '已登录'
      } else {
        isLogined.value = false
        loginStatus.value = '未登录'
      }
    } else {
      isLogined.value = false
      loginStatus.value = '未登录'
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    loginStatus.value = '检查失败'
  }
  loading.value = false
}

// 登录
function login() {
  loading.value = true
  loginStatus.value = '登录中...'

  try {
    const result = (window as any).Extention.Account.login({
      bizSrc: "weblogin",
      loginSrc: "a",
      qrcode: "a",
      from: "a",
      bShowModal: true,
      bTopWindow: false,
      a: "a"
    })
    console.log('登录结果:', result)

    setTimeout(() => {
      checkLoginState()
    }, 1000)
  } catch (error) {
    console.error('登录失败:', error)
    loginStatus.value = '登录失败'
    loading.value = false
  }
}

// 退登
function logout() {
  loading.value = true
  loginStatus.value = '退登中...'

  try {
    const result = (window as any).Extention.Account.logout({
      logoutSrc: "weblogout"
    })
    console.log('退登结果:', result)

    setTimeout(() => {
      checkLoginState()
    }, 1000)
  } catch (error) {
    console.error('退登失败:', error)
    loginStatus.value = '退登失败'
    loading.value = false
  }
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginState()

  // 监听登录
  try {
    (window as any).Extention.Account.addLoginListener(function(data: any) {
      console.log('登录事件:', data)
      setTimeout(() => checkLoginState(), 500)
    })

    // 监听退登
    (window as any).Extention.Account.addLogoutListener(function(data: any) {
      console.log('退登事件:', data)
      setTimeout(() => checkLoginState(), 500)
    })

    console.log('事件监听器注册成功')
  } catch (error) {
    console.error('注册事件监听器失败:', error)
  }
})
</script>

<template>
  <div class="about">
    <h1>WPS 2.0 JSAPI 登录</h1>

    <div class="status">
      状态: <span :style="{ color: isLogined ? 'green' : 'red' }">{{ loginStatus }}</span>
    </div>

    <div class="buttons">
      <button @click="checkLoginState" :disabled="loading">
        {{ loading ? '检查中...' : '检查登录状态' }}
      </button>

      <button @click="login" :disabled="loading || isLogined">
        {{ loading ? '登录中...' : '登录' }}
      </button>

      <button @click="logout" :disabled="loading || !isLogined">
        {{ loading ? '退登中...' : '退登' }}
      </button>
    </div>
  </div>
</template>

<style>
.about {
  padding: 20px;
  text-align: center;
}

.status {
  margin: 20px 0;
  font-size: 18px;
}

.buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>

