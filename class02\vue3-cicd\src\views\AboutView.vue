<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 登录状态
const isLogined = ref(false)
const loginStatus = ref('检查中...')
const loading = ref(false)
const userInfo = ref<any>(null)
const eventListenersRegistered = ref(false)
const debugInfo = ref<any>({})
const showDebugInfo = ref(false)

// 存储监听器引用，用于清理
let loginListener: any = null
let logoutListener: any = null

// 检查WPS 2.0 JSAPI环境
function checkWPSEnvironment() {
  const hasExtention = typeof (window as any).Extention !== 'undefined';
  const hasAccount = hasExtention && typeof (window as any).Extention.Account !== 'undefined';

  console.log('WPS环境检测:', {
    hasExtention,
    hasAccount,
    windowKeys: Object.keys(window).filter(k => k.includes('Ext')),
  });

  return hasAccount;
}

// 获取用户信息并检查登录状态
function getUserInfo() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    loginStatus.value = 'WPS 2.0环境未检测到'
    return
  }

  loading.value = true
  loginStatus.value = '检查登录状态中...'

  try {
    console.log('开始调用 Extention.Account.getUserInfo()')

    // 尝试不同的调用方式
    const accountAPI = (window as any).Extention.Account;
    console.log('Account API:', accountAPI);
    console.log('getUserInfo method:', accountAPI.getUserInfo);

    // 方式1：直接调用
    const result = accountAPI.getUserInfo();
    console.log('getUserInfo 调用结果:', result);

    // 检查是否是Promise
    if (result && typeof result.then === 'function') {
      // Promise方式
      result
        .then((data: any) => {
          console.log('Promise 用户信息:', data)
          handleUserInfoResult(data)
        })
        .catch((error: any) => {
          console.error('Promise 获取用户信息失败:', error)
          handleUserInfoError(error)
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      // 同步方式
      console.log('同步调用结果:', result)
      handleUserInfoResult(result)
      loading.value = false
    }

  } catch (error) {
    console.error('调用getUserInfo失败:', error)
    handleUserInfoError(error)
    loading.value = false
  }
}

// 处理用户信息结果
function handleUserInfoResult(result: any) {
  console.log('处理用户信息结果:', result)

  if (result && (result.userId || result.id || result.user_id)) {
    isLogined.value = true
    loginStatus.value = '已登录'
    userInfo.value = result
  } else if (result && result.code === 0) {
    // 有些API返回 {code: 0, data: {...}}
    const userData = result.data || result;
    if (userData && (userData.userId || userData.id || userData.user_id)) {
      isLogined.value = true
      loginStatus.value = '已登录'
      userInfo.value = userData
    } else {
      isLogined.value = false
      loginStatus.value = '未登录'
      userInfo.value = null
    }
  } else {
    isLogined.value = false
    loginStatus.value = '未登录'
    userInfo.value = null
  }
}

// 处理用户信息错误
function handleUserInfoError(error: any) {
  console.error('获取用户信息失败:', error)
  isLogined.value = false
  loginStatus.value = '未登录'
  userInfo.value = null
}

// 登录
function login() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    return
  }

  loading.value = true
  loginStatus.value = '登录中...'

  try {
    ;(window as any).Extention.Account.login({
      bizSrc: "weblogin",
      loginSrc: "a",
      qrcode: "a",
      from: "a",
      bShowModal: true,
      bTopWindow: false,
      a: "a"
    })
    .then((result: any) => {
      console.log('登录结果:', result)
      // 登录后重新获取用户信息
      setTimeout(() => {
        getUserInfo()
      }, 1000)
    })
    .catch((error: any) => {
      console.error('登录失败:', error)
      loginStatus.value = '登录失败'
      loading.value = false
    })
  } catch (error) {
    console.error('调用login失败:', error)
    loginStatus.value = '登录失败'
    loading.value = false
  }
}

// 退登
function logout() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    return
  }

  loading.value = true
  loginStatus.value = '退登中...'

  try {
    ;(window as any).Extention.Account.logout({
      logoutSrc: "weblogout"
    })
    .then((result: any) => {
      console.log('退登结果:', result)
      // 退登后重新获取用户信息
      setTimeout(() => {
        getUserInfo()
      }, 1000)
    })
    .catch((error: any) => {
      console.error('退登失败:', error)
      loginStatus.value = '退登失败'
      loading.value = false
    })
  } catch (error) {
    console.error('调用logout失败:', error)
    loginStatus.value = '退登失败'
    loading.value = false
  }
}

// 处理登录状态变化
function handleLoginStateChange(eventType: 'login' | 'logout', data?: any) {
  console.log(`${eventType === 'login' ? '登录' : '退登'}事件触发:`, data)

  // 延迟检查登录状态，确保状态已更新
  setTimeout(() => {
    getUserInfo()
  }, 500)
}

// 注册事件监听器
function registerEventListeners() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到，无法注册事件监听器')
    return
  }

  try {
    // 监听登录事件
    loginListener = (window as any).Extention.Account.addLoginListener((data: any) => {
      handleLoginStateChange('login', data)
    })

    // 监听退登事件
    logoutListener = (window as any).Extention.Account.addLogoutListener((data: any) => {
      handleLoginStateChange('logout', data)
    })

    eventListenersRegistered.value = true
    console.log('WPS 2.0 JSAPI 事件监听器已注册')
  } catch (error) {
    console.error('注册事件监听器失败:', error)
  }
}

// 清理事件监听器
function cleanupEventListeners() {
  if (loginListener) {
    try {
      // 如果有移除监听器的方法，在这里调用
      loginListener = null
    } catch (error) {
      console.error('清理登录监听器失败:', error)
    }
  }

  if (logoutListener) {
    try {
      // 如果有移除监听器的方法，在这里调用
      logoutListener = null
    } catch (error) {
      console.error('清理退登监听器失败:', error)
    }
  }

  eventListenersRegistered.value = false
}

// 调试WPS环境
function debugWPSEnvironment() {
  const debug = {
    hasWindow: typeof window !== 'undefined',
    hasExtention: typeof (window as any).Extention !== 'undefined',
    hasAccount: false,
    accountMethods: [],
    windowKeys: Object.keys(window).filter(k => k.toLowerCase().includes('ext') || k.toLowerCase().includes('wps')),
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  };

  if (debug.hasExtention) {
    debug.hasAccount = typeof (window as any).Extention.Account !== 'undefined';
    if (debug.hasAccount) {
      const account = (window as any).Extention.Account;
      debug.accountMethods = Object.getOwnPropertyNames(account).filter(prop => typeof account[prop] === 'function');
    }
  }

  debugInfo.value = debug;
  showDebugInfo.value = true;
  console.log('WPS环境调试信息:', debug);
}

// 备用方法：检查登录状态
function checkLoginStateAlternative() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    loginStatus.value = 'WPS 2.0环境未检测到'
    return
  }

  try {
    // 尝试使用其他方法检查登录状态
    const accountAPI = (window as any).Extention.Account;

    // 尝试方法1: isLogin 属性或方法
    if (typeof accountAPI.isLogin === 'function') {
      const isLogin = accountAPI.isLogin();
      console.log('isLogin() 结果:', isLogin);
      isLogined.value = !!isLogin;
      loginStatus.value = isLogined.value ? '已登录' : '未登录';
      return;
    } else if (typeof accountAPI.isLogin !== 'undefined') {
      console.log('isLogin 属性:', accountAPI.isLogin);
      isLogined.value = !!accountAPI.isLogin;
      loginStatus.value = isLogined.value ? '已登录' : '未登录';
      return;
    }

    // 尝试方法2: isLogined 属性或方法
    if (typeof accountAPI.isLogined === 'function') {
      const isLogined = accountAPI.isLogined();
      console.log('isLogined() 结果:', isLogined);
      isLogined.value = !!isLogined;
      loginStatus.value = isLogined.value ? '已登录' : '未登录';
      return;
    } else if (typeof accountAPI.isLogined !== 'undefined') {
      console.log('isLogined 属性:', accountAPI.isLogined);
      isLogined.value = !!accountAPI.isLogined;
      loginStatus.value = isLogined.value ? '已登录' : '未登录';
      return;
    }

    console.log('没有找到可用的登录状态检查方法');

  } catch (error) {
    console.error('备用登录检查失败:', error);
  }
}

// 页面加载时初始化
onMounted(() => {
  // 检查初始登录状态
  getUserInfo()

  // 如果getUserInfo失败，尝试备用方法
  setTimeout(() => {
    if (!isLogined.value && loginStatus.value !== 'WPS 2.0环境未检测到') {
      console.log('尝试备用登录检查方法');
      checkLoginStateAlternative();
    }
  }, 1000);

  // 注册事件监听器
  registerEventListeners()
})

// 页面卸载时清理
onUnmounted(() => {
  cleanupEventListeners()
})
</script>

<template>
  <main class="about-container">
    <div class="login-panel">
      <h1>WPS 2.0 JSAPI 登录</h1>
      <p class="subtitle">动态图表弹窗容器 / 论文查重容器</p>

      <!-- 登录状态显示 -->
      <div class="status-section">
        <h2>登录状态</h2>
        <div class="status-display" :class="{ 'logged-in': isLogined, 'logged-out': !isLogined }">
          <div class="status-indicator"></div>
          <span class="status-text">{{ loginStatus }}</span>
        </div>

        <!-- 用户信息显示 -->
        <div v-if="userInfo && isLogined" class="user-info">
          <h3>用户信息</h3>
          <div class="user-details">
            <p><strong>用户ID:</strong> {{ userInfo.userId || '未知' }}</p>
            <p><strong>用户名:</strong> {{ userInfo.userName || '未知' }}</p>
            <p v-if="userInfo.email"><strong>邮箱:</strong> {{ userInfo.email }}</p>
          </div>
        </div>

        <!-- 事件监听器状态 -->
        <div class="event-listener-status" v-if="eventListenersRegistered">
          <div class="status-indicator active"></div>
          <span class="status-text">事件监听器已激活</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <h2>操作</h2>
        <div class="button-group">
          <button
            @click="getUserInfo"
            :disabled="loading"
            class="btn btn-primary"
          >
            {{ loading ? '检查中...' : '检查登录状态' }}
          </button>

          <button
            @click="checkLoginStateAlternative"
            :disabled="loading"
            class="btn btn-info"
          >
            备用检查
          </button>

          <button
            @click="debugWPSEnvironment"
            class="btn btn-warning"
          >
            调试环境
          </button>

          <button
            @click="login"
            :disabled="loading || isLogined"
            class="btn btn-success"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <button
            @click="logout"
            :disabled="loading || !isLogined"
            class="btn btn-danger"
          >
            {{ loading ? '退登中...' : '退登' }}
          </button>
        </div>
      </div>

      <!-- 说明信息 -->
      <div class="info-section">
        <h2>使用说明</h2>
        <ul>
          <li>请在WPS环境中打开此页面</li>
          <li>使用2.0 JSAPI接口（Extention.Account）</li>
          <li>支持动态图表弹窗容器和论文查重容器</li>
          <li>登录参数：bizSrc, loginSrc, qrcode, from, bShowModal, bTopWindow</li>
          <li>退登参数：logoutSrc</li>
          <li><strong>已添加事件监听器</strong>：自动监听登录/退登事件</li>
          <li>使用 <code>addLoginListener</code> 和 <code>addLogoutListener</code></li>
        </ul>
      </div>

      <!-- 调试信息 -->
      <div v-if="showDebugInfo" class="debug-section">
        <h2>调试信息</h2>
        <div class="debug-content">
          <pre>{{ JSON.stringify(debugInfo, null, 2) }}</pre>
        </div>
        <button @click="showDebugInfo = false" class="btn btn-secondary btn-sm">隐藏调试信息</button>
      </div>

      <!-- API对比 -->
      <div class="api-comparison">
        <h2>API 对比</h2>
        <div class="comparison-table">
          <div class="api-version">
            <h3>1.0 JSAPI (HomeView)</h3>
            <ul>
              <li>jsAsynCall 通用方法</li>
              <li>common.account.checkLoginState</li>
              <li>event.account.login/logout 事件</li>
            </ul>
          </div>
          <div class="api-version">
            <h3>2.0 JSAPI (AboutView)</h3>
            <ul>
              <li>Extention.Account 对象</li>
              <li>getUserInfo() Promise</li>
              <li>addLoginListener/addLogoutListener</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped>
.about-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-style: italic;
}

h2 {
  color: #555;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

h3 {
  color: #666;
  margin-bottom: 10px;
  font-size: 16px;
}

.status-section {
  margin-bottom: 30px;
}

.status-display {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  margin-bottom: 15px;
}

.status-display.logged-in {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-display.logged-out {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  background: #6c757d;
}

.logged-in .status-indicator {
  background: #28a745;
}

.logged-out .status-indicator {
  background: #dc3545;
}

.status-text {
  font-weight: 500;
  font-size: 16px;
}

.user-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #28a745;
  margin-bottom: 15px;
}

.user-details p {
  margin: 5px 0;
  color: #555;
}

.event-listener-status {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  font-size: 14px;
}

.event-listener-status .status-indicator.active {
  background: #17a2b8;
}

.action-section {
  margin-bottom: 30px;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: #138496;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background: #e0a800;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

.debug-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
  margin-bottom: 30px;
}

.debug-content {
  background: #212529;
  color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 15px;
}

.debug-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 13px;
}

.info-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  margin-bottom: 30px;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

.api-comparison {
  background: #fff3cd;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
}

.comparison-table {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 15px;
}

.api-version {
  background: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.api-version h3 {
  color: #495057;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e9ecef;
}

.api-version ul {
  margin: 0;
  padding-left: 20px;
}

.api-version li {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .comparison-table {
    grid-template-columns: 1fr;
  }
}
</style>
