<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 登录状态
const isLogined = ref(false)
const loginStatus = ref('检查中...')
const loading = ref(false)
const userInfo = ref<any>(null)
const eventListenersRegistered = ref(false)

// 存储监听器引用，用于清理
let loginListener: any = null
let logoutListener: any = null

// 检查WPS 2.0 JSAPI环境
function checkWPSEnvironment() {
  return typeof (window as any).Extention !== 'undefined' &&
         typeof (window as any).Extention.Account !== 'undefined'
}

// 获取用户信息并检查登录状态
function getUserInfo() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    loginStatus.value = 'WPS 2.0环境未检测到'
    return
  }

  loading.value = true
  loginStatus.value = '检查登录状态中...'

  try {
    ;(window as any).Extention.Account.getUserInfo()
      .then((result: any) => {
        console.log('用户信息:', result)
        if (result && result.userId) {
          isLogined.value = true
          loginStatus.value = '已登录'
          userInfo.value = result
        } else {
          isLogined.value = false
          loginStatus.value = '未登录'
          userInfo.value = null
        }
      })
      .catch((error: any) => {
        console.error('获取用户信息失败:', error)
        isLogined.value = false
        loginStatus.value = '未登录'
        userInfo.value = null
      })
      .finally(() => {
        loading.value = false
      })
  } catch (error) {
    console.error('调用getUserInfo失败:', error)
    loginStatus.value = '检查登录状态失败'
    loading.value = false
  }
}

// 登录
function login() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    return
  }

  loading.value = true
  loginStatus.value = '登录中...'

  try {
    ;(window as any).Extention.Account.login({
      bizSrc: "weblogin",
      loginSrc: "a",
      qrcode: "a",
      from: "a",
      bShowModal: true,
      bTopWindow: false,
      a: "a"
    })
    .then((result: any) => {
      console.log('登录结果:', result)
      // 登录后重新获取用户信息
      setTimeout(() => {
        getUserInfo()
      }, 1000)
    })
    .catch((error: any) => {
      console.error('登录失败:', error)
      loginStatus.value = '登录失败'
      loading.value = false
    })
  } catch (error) {
    console.error('调用login失败:', error)
    loginStatus.value = '登录失败'
    loading.value = false
  }
}

// 退登
function logout() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到')
    return
  }

  loading.value = true
  loginStatus.value = '退登中...'

  try {
    ;(window as any).Extention.Account.logout({
      logoutSrc: "weblogout"
    })
    .then((result: any) => {
      console.log('退登结果:', result)
      // 退登后重新获取用户信息
      setTimeout(() => {
        getUserInfo()
      }, 1000)
    })
    .catch((error: any) => {
      console.error('退登失败:', error)
      loginStatus.value = '退登失败'
      loading.value = false
    })
  } catch (error) {
    console.error('调用logout失败:', error)
    loginStatus.value = '退登失败'
    loading.value = false
  }
}

// 处理登录状态变化
function handleLoginStateChange(eventType: 'login' | 'logout', data?: any) {
  console.log(`${eventType === 'login' ? '登录' : '退登'}事件触发:`, data)

  // 延迟检查登录状态，确保状态已更新
  setTimeout(() => {
    getUserInfo()
  }, 500)
}

// 注册事件监听器
function registerEventListeners() {
  if (!checkWPSEnvironment()) {
    console.error('WPS 2.0 JSAPI环境未检测到，无法注册事件监听器')
    return
  }

  try {
    // 监听登录事件
    loginListener = (window as any).Extention.Account.addLoginListener((data: any) => {
      handleLoginStateChange('login', data)
    })

    // 监听退登事件
    logoutListener = (window as any).Extention.Account.addLogoutListener((data: any) => {
      handleLoginStateChange('logout', data)
    })

    eventListenersRegistered.value = true
    console.log('WPS 2.0 JSAPI 事件监听器已注册')
  } catch (error) {
    console.error('注册事件监听器失败:', error)
  }
}

// 清理事件监听器
function cleanupEventListeners() {
  if (loginListener) {
    try {
      // 如果有移除监听器的方法，在这里调用
      loginListener = null
    } catch (error) {
      console.error('清理登录监听器失败:', error)
    }
  }

  if (logoutListener) {
    try {
      // 如果有移除监听器的方法，在这里调用
      logoutListener = null
    } catch (error) {
      console.error('清理退登监听器失败:', error)
    }
  }

  eventListenersRegistered.value = false
}

// 页面加载时初始化
onMounted(() => {
  // 检查初始登录状态
  getUserInfo()

  // 注册事件监听器
  registerEventListeners()
})

// 页面卸载时清理
onUnmounted(() => {
  cleanupEventListeners()
})
</script>

<template>
  <main class="about-container">
    <div class="login-panel">
      <h1>WPS 2.0 JSAPI 登录</h1>
      <p class="subtitle">动态图表弹窗容器 / 论文查重容器</p>

      <!-- 登录状态显示 -->
      <div class="status-section">
        <h2>登录状态</h2>
        <div class="status-display" :class="{ 'logged-in': isLogined, 'logged-out': !isLogined }">
          <div class="status-indicator"></div>
          <span class="status-text">{{ loginStatus }}</span>
        </div>

        <!-- 用户信息显示 -->
        <div v-if="userInfo && isLogined" class="user-info">
          <h3>用户信息</h3>
          <div class="user-details">
            <p><strong>用户ID:</strong> {{ userInfo.userId || '未知' }}</p>
            <p><strong>用户名:</strong> {{ userInfo.userName || '未知' }}</p>
            <p v-if="userInfo.email"><strong>邮箱:</strong> {{ userInfo.email }}</p>
          </div>
        </div>

        <!-- 事件监听器状态 -->
        <div class="event-listener-status" v-if="eventListenersRegistered">
          <div class="status-indicator active"></div>
          <span class="status-text">事件监听器已激活</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <h2>操作</h2>
        <div class="button-group">
          <button
            @click="getUserInfo"
            :disabled="loading"
            class="btn btn-primary"
          >
            {{ loading ? '检查中...' : '检查登录状态' }}
          </button>

          <button
            @click="login"
            :disabled="loading || isLogined"
            class="btn btn-success"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <button
            @click="logout"
            :disabled="loading || !isLogined"
            class="btn btn-danger"
          >
            {{ loading ? '退登中...' : '退登' }}
          </button>
        </div>
      </div>

      <!-- 说明信息 -->
      <div class="info-section">
        <h2>使用说明</h2>
        <ul>
          <li>请在WPS环境中打开此页面</li>
          <li>使用2.0 JSAPI接口（Extention.Account）</li>
          <li>支持动态图表弹窗容器和论文查重容器</li>
          <li>登录参数：bizSrc, loginSrc, qrcode, from, bShowModal, bTopWindow</li>
          <li>退登参数：logoutSrc</li>
          <li><strong>已添加事件监听器</strong>：自动监听登录/退登事件</li>
          <li>使用 <code>addLoginListener</code> 和 <code>addLogoutListener</code></li>
        </ul>
      </div>

      <!-- API对比 -->
      <div class="api-comparison">
        <h2>API 对比</h2>
        <div class="comparison-table">
          <div class="api-version">
            <h3>1.0 JSAPI (HomeView)</h3>
            <ul>
              <li>jsAsynCall 通用方法</li>
              <li>common.account.checkLoginState</li>
              <li>event.account.login/logout 事件</li>
            </ul>
          </div>
          <div class="api-version">
            <h3>2.0 JSAPI (AboutView)</h3>
            <ul>
              <li>Extention.Account 对象</li>
              <li>getUserInfo() Promise</li>
              <li>addLoginListener/addLogoutListener</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped>
.about-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-style: italic;
}

h2 {
  color: #555;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

h3 {
  color: #666;
  margin-bottom: 10px;
  font-size: 16px;
}

.status-section {
  margin-bottom: 30px;
}

.status-display {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  margin-bottom: 15px;
}

.status-display.logged-in {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-display.logged-out {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  background: #6c757d;
}

.logged-in .status-indicator {
  background: #28a745;
}

.logged-out .status-indicator {
  background: #dc3545;
}

.status-text {
  font-weight: 500;
  font-size: 16px;
}

.user-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #28a745;
  margin-bottom: 15px;
}

.user-details p {
  margin: 5px 0;
  color: #555;
}

.event-listener-status {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  font-size: 14px;
}

.event-listener-status .status-indicator.active {
  background: #17a2b8;
}

.action-section {
  margin-bottom: 30px;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.info-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  margin-bottom: 30px;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

.api-comparison {
  background: #fff3cd;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
}

.comparison-table {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 15px;
}

.api-version {
  background: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.api-version h3 {
  color: #495057;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e9ecef;
}

.api-version ul {
  margin: 0;
  padding-left: 20px;
}

.api-version li {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .comparison-table {
    grid-template-columns: 1fr;
  }
}
</style>
