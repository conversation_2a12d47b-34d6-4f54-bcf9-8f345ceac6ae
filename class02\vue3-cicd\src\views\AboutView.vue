<script setup lang="ts">
import { ref, onMounted } from 'vue'

const isLogined = ref(false)
const hasPrivilege = ref(false)
const isChecking = ref(false)

function checkShow() {
  // 这个蜂巢配置的结果中，返回show字段，值为bool
 isChecking.value = window.Extention.Honeycomb.getMostApplicableDataUnderModuleGroup({ projectID: '60', moduleGroupID: '55748' });
  console.log('是否显示:', isChecking.value.result);
  // 字符串解析为json
  isChecking.value = JSON.parse(isChecking.value.result).show;
  console.log('是否显示:', isChecking.value);
}

// 检查登录状态
function checkLoginState() {
  try {
    const response = (window as any).Extention.Account.getUserInfo()
    console.log('getUserInfo结果:', response)

    // 检查返回格式 {code: true, result: {...}}
    if (response && response.code && response.result) {
      const result = response.result
      if (result.userId) {
        isLogined.value = true
      } else {
        isLogined.value = false
      }
    } else {
      isLogined.value = false
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
  }
}

// 登录
function login() {


  try {
    const result = (window as any).Extention.Account.login({
      bizSrc: "weblogin",
      loginSrc: "a",
      qrcode: "a",
      from: "a",
      bShowModal: true,
      bTopWindow: false,
      a: "a"
    })
    console.log('登录结果:', result)

    setTimeout(() => {
      checkLoginState()
    }, 1000)
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 退登
function logout() {

  try {
    const result = (window as any).Extention.Account.logout({
      logoutSrc: "weblogout"
    })
    console.log('退登结果:', result)

    setTimeout(() => {
      checkLoginState()
    }, 1000)
  } catch (error) {
    console.error('退登失败:', error)
  }
}

// 检查权限
function checkPrivilege() {

    const result = (window as any).Extention.Account.Privilege.getPrivilegeInfo({
      privilege_ids: ['resume_package_new']
    })
    console.log('权限检查结果:', result)

    if (result && result.code && result.result) {
      const privileges = result.result
      if (privileges.resume_package_new && privileges.resume_package_new.has_privilege) {
        hasPrivilege.value = true
        alert('权限检查成功：您有此权限！')
      } else {
        hasPrivilege.value = false
        alert('权限检查：您暂无此权限，请支付获取')
      }
    } 
  }

// 调起支付
function openPayment() {
    const result = (window as any).Extention.DocerV2.openDocerUnifyPayDlg({
      url: 'https://vip.wps.cn/vcl_svr/static/docerpay'
    })
    console.log('支付调起结果:', result)
    alert('支付页面已打开')
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginState()
  checkShow()
  // 监听登录
  try {
    (window as any).Extention.Account.addLoginListener(function(data: any) {
      console.log('登录事件:', data)
      setTimeout(() => checkLoginState(), 500)
    })

    // 监听退登
    (window as any).Extention.Account.addLogoutListener(function(data: any) {
      console.log('退登事件:', data)
      setTimeout(() => checkLoginState(), 500)
    })

    console.log('事件监听器注册成功')
  } catch (error) {
    console.error('注册事件监听器失败:', error)
  }
})
</script>

<template>
  <div class="about">
    <h1>WPS 2.0 JSAPI 登录</h1>
    <div class="buttons">
      <button @click="login" :disabled="isLogined">
                  登录
      </button>

      <button @click="logout" :disabled="!isLogined">
                  退登
      </button>
    </div>

    <div class="buttons">
      <button v-if="isChecking" @click="checkPrivilege" :disabled="!isLogined">
        检查权限
      </button>

      <button @click="openPayment" :disabled="!isLogined">
        调起支付
      </button>
    </div>
  </div>
</template>

<style>
.about {
  padding: 20px;
  text-align: center;
}

.status {
  margin: 20px 0;
  font-size: 18px;
}

.buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>

