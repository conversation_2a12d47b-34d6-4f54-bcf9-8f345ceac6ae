<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 登录状态
const isLogined = ref(false)
const loginStatus = ref('检查中...')
const loading = ref(false)

// WPS JSAPI 通用调用方法
function jsAsynCall(methodName: string, params: any, callback: (res: string) => void) {
  const callbackName = `${methodName?.split('.')?.join('')}_async_callback_${Date.now()}`

  // 设置回调函数
  ;(window as any)[callbackName] = (res: string) => {
    console.log('JSAPI Response:', atob(res))
    callback(atob(res))
    // 清理回调函数
    delete (window as any)[callbackName]
  }

  // 调用WPS JSAPI
  if ((window as any).wpsQuery) {
    ;(window as any).wpsQuery({
      request: 'jsAsynCall("' + btoa(`{"method": "${methodName}", "params": ${JSON.stringify(params)}, "callback": "${callbackName}"}`) + '")'
    })
  } else {
    console.error('WPS环境未检测到，请在WPS中打开此页面')
    loginStatus.value = 'WPS环境未检测到'
  }
}

// 检查登录状态
function checkLoginState() {
  loading.value = true
  loginStatus.value = '检查登录状态中...'

  jsAsynCall('common.account.checkLoginState', {}, (res) => {
    try {
      const resJson = JSON.parse(res)
      isLogined.value = resJson.isLogined
      loginStatus.value = resJson.isLogined ? '已登录' : '未登录'
      console.log('登录状态:', resJson.isLogined)
    } catch (error) {
      console.error('解析登录状态失败:', error)
      loginStatus.value = '检查登录状态失败'
    } finally {
      loading.value = false
    }
  })
}

// 登录
function login() {
  loading.value = true
  loginStatus.value = '登录中...'

  jsAsynCall('login', {from: 'docermall_win', qrcode: 'docer'}, (res) => {
    try {
      console.log('登录结果:', res)
      // 登录后重新检查状态
      setTimeout(() => {
        checkLoginState()
      }, 1000)
    } catch (error) {
      console.error('登录失败:', error)
      loginStatus.value = '登录失败'
      loading.value = false
    }
  })
}

// 退登
function logout() {
  loading.value = true
  loginStatus.value = '退登中...'

  jsAsynCall('common.account.logout', {from: 'docermall_win', qrcode: 'docer'}, (res) => {
    try {
      console.log('退登结果:', res)
      // 退登后重新检查状态
      setTimeout(() => {
        checkLoginState()
      }, 1000)
    } catch (error) {
      console.error('退登失败:', error)
      loginStatus.value = '退登失败'
      loading.value = false
    }
  })
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginState()
})
</script>

<template>
  <main class="home-container">
    <div class="login-panel">
      <h1>WPS 稻壳商城登录</h1>

      <!-- 登录状态显示 -->
      <div class="status-section">
        <h2>登录状态</h2>
        <div class="status-display" :class="{ 'logged-in': isLogined, 'logged-out': !isLogined }">
          <div class="status-indicator"></div>
          <span class="status-text">{{ loginStatus }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <h2>操作</h2>
        <div class="button-group">
          <button
            @click="checkLoginState"
            :disabled="loading"
            class="btn btn-primary"
          >
            {{ loading ? '检查中...' : '检查登录状态' }}
          </button>

          <button
            @click="login"
            :disabled="loading || isLogined"
            class="btn btn-success"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <button
            @click="logout"
            :disabled="loading || !isLogined"
            class="btn btn-danger"
          >
            {{ loading ? '退登中...' : '退登' }}
          </button>
        </div>
      </div>

      <!-- 说明信息 -->
      <div class="info-section">
        <h2>使用说明</h2>
        <ul>
          <li>请在WPS环境中打开此页面</li>
          <li>使用1.0 JSAPI接口，实现登录功能</li>
          <li>如果当客户已过登录，按钮显示"退出登录"，点击按钮调起退出登录对话框</li>
          <li>按钮显示"登录"，点击按钮调起登录对话框</li>
          <li>用户登录态变化后，需要更新按钮状态</li>
          <li>新用户登录后台会有各种提示，此时确保页面运行</li>
        </ul>
      </div>
    </div>
  </main>
</template>

<style scoped>
.home-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
}

h2 {
  color: #555;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.status-section {
  margin-bottom: 30px;
}

.status-display {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.status-display.logged-in {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-display.logged-out {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  background: #6c757d;
}

.logged-in .status-indicator {
  background: #28a745;
}

.logged-out .status-indicator {
  background: #dc3545;
}

.status-text {
  font-weight: 500;
  font-size: 16px;
}

.action-section {
  margin-bottom: 30px;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.info-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

@media (max-width: 600px) {
  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>
