<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 登录状态
const isLogined = ref(false)
const loginStatus = ref('检查中...')
const loading = ref(false)
const eventListenersRegistered = ref(false)

// WPS JSAPI 通用调用方法
function jsAsynCall(methodName: string, params: any, callback: (res: string) => void) {
  // 使用更简单的回调函数名，避免特殊字符
  const timestamp = Date.now()
  const callbackName = `jsapi_callback_${timestamp}`

  // 设置回调函数
  ;(window as any)[callbackName] = (res: string) => {
    try {
      console.log('JSAPI Response:', atob(res))
      callback(atob(res))
    } catch (error) {
      console.error('JSAPI回调处理错误:', error)
    } finally {
      // 延迟清理回调函数，确保WPS有足够时间调用
      setTimeout(() => {
        if ((window as any)[callbackName]) {
          delete (window as any)[callbackName]
        }
      }, 1000)
    }
  }

  // 调用WPS JSAPI
  if ((window as any).wpsQuery) {
    const requestData = {
      method: methodName,
      params: params,
      callback: callbackName
    }

    ;(window as any).wpsQuery({
      request: 'jsAsynCall("' + btoa(JSON.stringify(requestData)) + '")'
    })
  } else {
    console.error('WPS环境未检测到，请在WPS中打开此页面')
    loginStatus.value = 'WPS环境未检测到'
  }
}

// 注册事件监听器
function registerEventListener(listEventName: string, callback?: (res: string) => void) {
  const method = 'common.event.addEventListener'
  const globalEvent = `${listEventName.split('.').join('')}_async_callback`

  // 注册全局回调函数
  ;(window as any)[globalEvent] = (res: string) => {
    try {
      const decodedRes = atob(res)
      console.log(`事件监听 [${listEventName}]:`, decodedRes)
      if (callback) {
        callback(decodedRes)
      }
    } catch (error) {
      console.error(`事件监听处理错误 [${listEventName}]:`, error)
    }
  }

  const params = {
    eventName: listEventName,
    callbackName: globalEvent,
    isResultBase64: true,
    _config: {
      isChangeDot: true,
    },
  }

  // 调用jsAsynCall注册事件监听，不需要回调
  jsAsynCall(method, params, (res) => {
    console.log(`事件监听器注册结果 [${listEventName}]:`, res)
  })
}

// 检查登录状态
function checkLoginState() {
  loading.value = true
  loginStatus.value = '检查登录状态中...'

  jsAsynCall('common.account.checkLoginState', {}, (res) => {
    try {
      const resJson = JSON.parse(res)
      isLogined.value = resJson.isLogined
      loginStatus.value = resJson.isLogined ? '已登录' : '未登录'
      console.log('登录状态:', resJson.isLogined)
    } catch (error) {
      console.error('解析登录状态失败:', error)
      loginStatus.value = '检查登录状态失败'
    } finally {
      loading.value = false
    }
  })
}

// 登录
function login() {
  loading.value = true
  loginStatus.value = '登录中...'

  jsAsynCall('login', {from: 'docermall_win', qrcode: 'docer'}, (res) => {
    try {
      console.log('登录结果:', res)

      // 尝试解析响应（用于调试）
      try {
        JSON.parse(res)
        console.log('登录响应解析成功')
      } catch (parseError) {
        console.log('登录响应不是JSON格式:', res)
      }

      // 登录后重新检查状态
      setTimeout(() => {
        checkLoginState()
      }, 1500)

    } catch (error) {
      console.error('登录处理失败:', error)
      loginStatus.value = '登录失败'
      loading.value = false
    }
  })
}

// 退登
function logout() {
  loading.value = true
  loginStatus.value = '退登中...'

  jsAsynCall('common.account.logout', {from: 'docermall_win', qrcode: 'docer'}, (res) => {
    try {
      console.log('退登结果:', res)

      // 尝试解析响应（用于调试）
      try {
        JSON.parse(res)
        console.log('退登响应解析成功')
      } catch (parseError) {
        console.log('退登响应不是JSON格式:', res)
      }

      // 退登后重新检查状态
      setTimeout(() => {
        checkLoginState()
      }, 1500)

    } catch (error) {
      console.error('退登处理失败:', error)
      loginStatus.value = '退登失败'
      loading.value = false
    }
  })
}

// 处理登录状态变化
function handleLoginStateChange(eventType: 'login' | 'logout', res: string) {
  console.log(`${eventType === 'login' ? '登录' : '退登'}事件触发:`, res)

  // 解析事件数据
  try {
    const eventData = JSON.parse(res)
    console.log('事件数据:', eventData)
  } catch (error) {
    console.log('事件数据不是JSON格式:', res)
  }

  // 延迟检查登录状态，确保状态已更新
  setTimeout(() => {
    checkLoginState()
  }, 500)
}

// 页面加载时检查登录状态并注册事件监听器
onMounted(() => {
  // 检查初始登录状态
  checkLoginState()

  // 注册登录事件监听
  registerEventListener('event.account.login', (res) => {
    handleLoginStateChange('login', res)
  })

  // 注册退登事件监听
  registerEventListener('event.account.logout', (res) => {
    handleLoginStateChange('logout', res)
  })

  console.log('事件监听器已注册')
  eventListenersRegistered.value = true
})
</script>

<template>
  <main class="home-container">
    <div class="login-panel">
      <h1>WPS 稻壳商城登录</h1>

      <!-- 登录状态显示 -->
      <div class="status-section">
        <h2>登录状态</h2>
        <div class="status-display" :class="{ 'logged-in': isLogined, 'logged-out': !isLogined }">
          <div class="status-indicator"></div>
          <span class="status-text">{{ loginStatus }}</span>
        </div>

        <!-- 事件监听器状态 -->
        <div class="event-listener-status" v-if="eventListenersRegistered">
          <div class="status-indicator active"></div>
          <span class="status-text">事件监听器已激活</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <h2>操作</h2>
        <div class="button-group">
          <button
            @click="checkLoginState"
            :disabled="loading"
            class="btn btn-primary"
          >
            {{ loading ? '检查中...' : '检查登录状态' }}
          </button>

          <button
            @click="login"
            :disabled="loading || isLogined"
            class="btn btn-success"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <button
            @click="logout"
            :disabled="loading || !isLogined"
            class="btn btn-danger"
          >
            {{ loading ? '退登中...' : '退登' }}
          </button>
        </div>
      </div>

      <!-- 说明信息 -->
      <div class="info-section">
        <h2>使用说明</h2>
        <ul>
          <li>请在WPS环境中打开此页面</li>
          <li>使用1.0 JSAPI接口，实现登录功能</li>
          <li>如果当客户已过登录，按钮显示"退出登录"，点击按钮调起退出登录对话框</li>
          <li>按钮显示"登录"，点击按钮调起登录对话框</li>
          <li>用户登录态变化后，需要更新按钮状态</li>
          <li>新用户登录后台会有各种提示，此时确保页面运行</li>
          <li><strong>已添加事件监听器</strong>：自动监听登录/退登事件，无需手动刷新</li>
          <li>使用 <code>event.account.login</code> 和 <code>event.account.logout</code> 事件</li>
        </ul>
      </div>
    </div>
  </main>
</template>

<style scoped>
.home-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
}

h2 {
  color: #555;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.status-section {
  margin-bottom: 30px;
}

.status-display {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.status-display.logged-in {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-display.logged-out {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  background: #6c757d;
}

.logged-in .status-indicator {
  background: #28a745;
}

.logged-out .status-indicator {
  background: #dc3545;
}

.event-listener-status {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  margin-top: 10px;
  font-size: 14px;
}

.event-listener-status .status-indicator.active {
  background: #17a2b8;
}

.status-text {
  font-weight: 500;
  font-size: 16px;
}

.action-section {
  margin-bottom: 30px;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.info-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

@media (max-width: 600px) {
  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>
