{"name": "vue3-cicd", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/vue-router": "^2.0.0", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-vue": "^6.0.0", "npm-run-all": "^4.1.5", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}, "packageManager": "pnpm@10.3.0+sha512.ee592eda8815a8a293c206bb0917c4bb0ff274c50def7cbc17be05ec641fc2d1b02490ce660061356bd0d126a4d7eb2ec8830e6959fb8a447571c631d5a2442d"}